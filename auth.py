import requests
import streamlit as st
import logging
import json
import sqlite3
from typing import Dict, <PERSON><PERSON>, Optional
from datetime import datetime, date, timedelta
import os
from pathlib import Path
from dotenv import load_dotenv
import extra_streamlit_components as stx

load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

AUTH_API_URL = "https://api.auth.wissenresearch.com/auth/login"

# Database setup
DB_PATH = "user_credits.db"

# Initialize cookie manager directly without caching
cookie_manager = None


def init_cookie_manager():
    """Initialize the cookie manager without caching"""
    global cookie_manager
    if cookie_manager is None:
        cookie_manager = stx.CookieManager()
    return cookie_manager


def init_database():
    """Initialize SQLite database for user credits"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Create users table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS user_credits (
        user_id INTEGER PRIMARY KEY,
        daily_credits_used INTEGER DEFAULT 0,
        total_credits_used INTEGER DEFAULT 0,
        last_request_date TEXT
    )
    ''')

    conn.commit()
    conn.close()
    logger.info("Database initialized")


def get_user_credits(user_id):
    """Get user credit information from database"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Check if user exists in database
    cursor.execute("SELECT * FROM user_credits WHERE user_id = ?", (user_id,))
    user_data = cursor.fetchone()

    today = date.today().isoformat()

    if not user_data:
        # Create new user entry
        cursor.execute(
            "INSERT INTO user_credits (user_id, daily_credits_used, total_credits_used, last_request_date) VALUES (?, 0, 0, ?)",
            (user_id, today)
        )
        conn.commit()
        user_data = (user_id, 0, 0, today)

    # Reset daily credits if it's a new day
    last_request_date = user_data[3]
    daily_credits_used = user_data[1]
    total_credits_used = user_data[2]

    if last_request_date != today:
        cursor.execute(
            "UPDATE user_credits SET daily_credits_used = 0, last_request_date = ? WHERE user_id = ?",
            (today, user_id)
        )
        conn.commit()
        daily_credits_used = 0

    conn.close()

    return {
        "daily_credits_used": daily_credits_used,
        "total_credits_used": total_credits_used,
        "last_request_date": last_request_date
    }


def use_credit(user_id):
    """Use one credit for the user and update database"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get current credit usage
    cursor.execute("SELECT daily_credits_used, total_credits_used FROM user_credits WHERE user_id = ?", (user_id,))
    result = cursor.fetchone()

    if not result:
        # This shouldn't happen if get_user_credits is called first, but just in case
        today = date.today().isoformat()
        cursor.execute(
            "INSERT INTO user_credits (user_id, daily_credits_used, total_credits_used, last_request_date) VALUES (?, 1, 1, ?)",
            (user_id, today)
        )
        daily_credits_used = 1
        total_credits_used = 1
    else:
        daily_credits_used = result[0] + 1
        total_credits_used = result[1] + 1

        cursor.execute(
            "UPDATE user_credits SET daily_credits_used = ?, total_credits_used = ? WHERE user_id = ?",
            (daily_credits_used, total_credits_used, user_id)
        )

    conn.commit()
    conn.close()

    logger.info(f"Credit used for user {user_id}. Daily: {daily_credits_used}, Total: {total_credits_used}")

    return {
        "daily_credits_used": daily_credits_used,
        "total_credits_used": total_credits_used
    }


def check_credits_available(user_id):
    """
    Check if user has credits available
    Returns (available, message) tuple
    """
    # Constants for credit limits
    DAILY_LIMIT = int(os.getenv("DAILY_LIMIT", 15))
    TOTAL_LIMIT = int(os.getenv("TOTAL_LIMIT", 50))

    # Get user credit data
    user_credits = get_user_credits(user_id)

    # Check daily limit
    daily_remaining = DAILY_LIMIT - user_credits["daily_credits_used"]
    if daily_remaining <= 0:
        return False, "⚠️ You've reached your daily limit of 15 credits. Please try again tomorrow."

    # Check total limit
    total_remaining = TOTAL_LIMIT - user_credits["total_credits_used"]
    if total_remaining <= 0:
        return False, "⚠️ You've reached your total limit of 50 credits. Please contact support for more credits."

    return True, ""

def logout():
    """Logout user and clear authentication data"""
    clear_authentication()
    st.success("Logged out successfully!")
    st.rerun()


def display_credits_sidebar(user_id):
    """Display credit information in the sidebar"""

    if st.button("Logout"):
        logout()


    # Constants for credit limits
    DAILY_LIMIT = 15
    TOTAL_LIMIT = 50

    # Get user credit data
    user_credits = get_user_credits(user_id)

    daily_used = user_credits["daily_credits_used"]
    total_used = user_credits["total_credits_used"]

    daily_remaining = DAILY_LIMIT - daily_used
    total_remaining = TOTAL_LIMIT - total_used

    st.sidebar.markdown("---")
    st.sidebar.subheader("Credits")

    # Display daily credits with appropriate color
    if daily_remaining <= 0:
        st.sidebar.error(f"Daily Credits: 0/{DAILY_LIMIT}")
        st.sidebar.warning("You've used all your daily credits. Credits will reset tomorrow.")
    elif daily_remaining <= 3:
        st.sidebar.warning(f"Daily Credits: {daily_remaining}/{DAILY_LIMIT}")
    else:
        st.sidebar.info(f"Daily Credits: {daily_remaining}/{DAILY_LIMIT}")

    # Display total credits with appropriate color
    if total_remaining <= 0:
        st.sidebar.error(f"Total Credits: 0/{TOTAL_LIMIT}")
        st.sidebar.warning("You've used all your total credits. Please contact support.")
    elif total_remaining <= 5:
        st.sidebar.warning(f"Total Credits: {total_remaining}/{TOTAL_LIMIT}")
    else:
        st.sidebar.success(f"Total Credits: {total_remaining}/{TOTAL_LIMIT}")



    return {
        "daily_remaining": daily_remaining,
        "total_remaining": total_remaining
    }


def login(email: str, password: str, totp_token: Optional[str] = None) -> Tuple[bool, str, Optional[Dict], bool]:
    """
    Authenticate user with email/password and optional TOTP token.
    Returns:
        (success, message, user_data, otp_required)
    """
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0"
    }

    payload = {"email": email, "password": password}
    if totp_token:
        payload["totp_token"] = totp_token

    try:
        response = requests.post(AUTH_API_URL, headers=headers, json=payload)
        logger.info(f"API response code: {response.status_code}")
        data = response.json()

        if response.status_code == 200:
            return True, "Login successful", data, False
        elif response.status_code == 400 and "TOTP" in data.get("error", ""):
            return False, data.get("error", "OTP required"), None, True
        else:
            return False, data.get("message", "Authentication failed"), None, False

    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return False, f"Login error: {str(e)}", None, False


def is_cookie_valid(auth_cookie_data):
    """
    Validate cookie data and check expiry with session token validation
    Returns True if valid, False otherwise
    """
    try:
        if not auth_cookie_data:
            return False

        # Check if cookie has expiry and if it's expired
        if "expiry" in auth_cookie_data:
            current_time = datetime.now().timestamp()
            if auth_cookie_data["expiry"] <= current_time:
                logger.info("Cookie expired")
                return False

        # Check if required fields are present
        if "user_id" not in auth_cookie_data or "user_data" not in auth_cookie_data:
            logger.warning("Cookie missing required fields")
            return False

        # Validate session token for better isolation
        cookie_session_token = auth_cookie_data.get("session_token")
        current_session_token = st.session_state.get("session_token")

        # If we have session tokens, they must match
        if cookie_session_token and current_session_token:
            if cookie_session_token != current_session_token:
                logger.info("Session token mismatch - possible session sharing")
                return False
        elif cookie_session_token and not current_session_token:
            # Cookie has session token but session state doesn't - restore it
            st.session_state.session_token = cookie_session_token

        return True

    except Exception as e:
        logger.error(f"Error validating cookie: {str(e)}")
        return False


def clear_authentication():
    """Clear all authentication data"""
    global cookie_manager

    # Clear session state
    st.session_state.authenticated = False
    st.session_state.user_data = None
    st.session_state.user_id = None
    st.session_state.otp_required = False
    st.session_state.email = ""
    st.session_state.password = ""

    # Clear specific session state keys instead of clearing all
    keys_to_clear = [
        'authenticated', 'user_data', 'user_id', 'otp_required',
        'email', 'password', 'remember_me', 'session_token'
    ]
    for key in keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]

    # Clear cookie if cookie manager is available
    if cookie_manager:
        try:
            cookie_manager.delete("wissen_auth")
            logger.info("Authentication cookie cleared")
        except Exception as e:
            logger.error(f"Error clearing cookie: {str(e)}")


def set_persistent_cookie(user_data, remember_me=False):
    """Set authentication cookie with proper expiry and session isolation"""
    global cookie_manager

    if not cookie_manager:
        logger.error("Cookie manager not initialized")
        return

    try:
        # Generate a unique session token for better isolation
        import uuid
        session_token = str(uuid.uuid4())

        if remember_me:
            # Set 7-day persistent cookie
            expiry = (datetime.now() + timedelta(days=7)).timestamp()
            auth_cookie_data = {
                "user_id": user_data["id"],
                "user_data": user_data,
                "expiry": expiry,
                "persistent": True,
                "session_token": session_token
            }
            cookie_manager.set("wissen_auth", json.dumps(auth_cookie_data),
                               expires_at=datetime.now() + timedelta(days=7))
            logger.info(f"7-day persistent cookie set for user {user_data['id']} with session token")
        else:
            # Set session cookie (no expiry, should be cleared when browser closes)
            auth_cookie_data = {
                "user_id": user_data["id"],
                "user_data": user_data,
                "persistent": False,
                "session_token": session_token
            }
            # Don't set expires_at for session cookies
            cookie_manager.set("wissen_auth", json.dumps(auth_cookie_data))
            logger.info(f"Session cookie set for user {user_data['id']} with session token")

        # Store session token in session state for validation
        st.session_state.session_token = session_token

    except Exception as e:
        logger.error(f"Error setting cookie: {str(e)}")


def check_authentication():
    """
    Check if user is authenticated and handle login flow.
    Returns True if authenticated, False otherwise.
    """
    # Initialize database on first run
    if not os.path.exists(DB_PATH):
        init_database()

    # Initialize cookie manager
    global cookie_manager
    cookie_manager = init_cookie_manager()

    # Initialize session state if needed
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False
        st.session_state.user_data = None
        st.session_state.otp_required = False
        st.session_state.email = ""
        st.session_state.password = ""
        st.session_state.user_id = None
        st.session_state.remember_me = False
        st.session_state.session_token = None

    # Check for existing authentication in session state first
    if st.session_state.authenticated and st.session_state.user_id:
        return True

    # If not authenticated in session, check for auth cookie
    auth_cookie = None
    try:
        auth_cookie = cookie_manager.get("wissen_auth")
    except Exception as e:
        logger.error(f"Error reading cookie: {str(e)}")
        auth_cookie = None

    # If we have a cookie, validate and restore authentication
    if auth_cookie:
        try:
            auth_data = json.loads(auth_cookie)

            if is_cookie_valid(auth_data):
                # Restore authentication from valid cookie
                st.session_state.authenticated = True
                st.session_state.user_data = auth_data.get("user_data")
                st.session_state.user_id = auth_data.get("user_id")
                logger.info(f"User authenticated from cookie: {st.session_state.user_id}")
                return True
            else:
                # Invalid or expired cookie, clear it
                logger.info("Invalid cookie found, clearing")
                try:
                    cookie_manager.delete("wissen_auth")
                except:
                    pass  # Ignore errors when clearing invalid cookies

        except Exception as e:
            logger.error(f"Error parsing auth cookie: {str(e)}")
            # Clear malformed cookie
            try:
                cookie_manager.delete("wissen_auth")
            except:
                pass

    # If we reach here, user is not authenticated - show login form
    st.title("Wissen Research Login")

    # Step 1: Login Form
    if not st.session_state.otp_required:
        with st.form("login_form"):
            email = st.text_input("Email")
            password = st.text_input("Password", type="password")
            submit = st.form_submit_button("Login")

            if submit:
                if not email or not password:
                    st.error("Please enter both email and password")
                    return False

                success, message, user_data, otp_required = login(email, password)

                if success:
                    st.session_state.authenticated = True
                    st.session_state.user_data = user_data

                    # Extract user ID from response
                    if user_data and "id" in user_data:
                        st.session_state.user_id = user_data["id"]
                        logger.info(f"User logged in with ID: {st.session_state.user_id}")

                        # Set cookie based on remember me choice
                        set_persistent_cookie(user_data)

                    else:
                        logger.error("User ID not found in response data")
                        st.error("Authentication error: User ID not found")
                        clear_authentication()
                        return False

                    st.success("Login successful!")
                    st.rerun()
                elif otp_required:
                    st.session_state.otp_required = True
                    st.session_state.email = email
                    st.session_state.password = password
                    st.info("OTP required. Please check your email.")
                    st.rerun()
                else:
                    st.error(message)
                    return False

    # Step 2: OTP Form (shown only if required)
    elif st.session_state.otp_required:
        with st.form("otp_form"):
            st.subheader("Two-Factor Authentication")
            st.info("Please enter the OTP sent to your email")
            otp = st.text_input("OTP Code")
            otp_submit = st.form_submit_button("Verify")

            if otp_submit:
                if not otp:
                    st.error("Please enter the OTP code")
                    return False

                success, message, user_data, otp_required = login(
                    st.session_state.email,
                    st.session_state.password,
                    otp
                )

                if success:
                    st.session_state.authenticated = True
                    st.session_state.user_data = user_data
                    st.session_state.otp_required = False

                    # Extract user ID from response
                    if user_data and "id" in user_data:
                        st.session_state.user_id = user_data["id"]
                        logger.info(f"User logged in with ID: {st.session_state.user_id}")

                        # Set cookie based on remember me choice
                        remember_me = getattr(st.session_state, "remember_me", False)
                        set_persistent_cookie(user_data, remember_me)

                    else:
                        logger.error("User ID not found in response data")
                        st.error("Authentication error: User ID not found")
                        clear_authentication()
                        return False

                    st.success("Login successful!")
                    st.rerun()
                else:
                    st.error(message)
                    return False