import streamlit as st
from agno.agent import Agent
from dotenv import load_dotenv
from agno.models.google import Gemini
from utils import get_patent_full_details, LitigationSearch
from datetime import datetime, timedelta
import re
import pandas as pd
from io import BytesIO
import logging
import json
import os
import sqlite3
from utils import img_to_base64
from auth import check_authentication

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', force=True)

load_dotenv()

SERPAPI_API_KEY = os.environ.get("SERPAPI_API_KEY")
GROQ_API_KEY = os.environ.get("GROQ_API_KEY")
MODEL_ID = os.environ.get("MODEL_ID")
WISSEN_DB_PATH = os.getenv("WISSEN_DB_PATH")

missing_keys = []
if not SERPAPI_API_KEY:
    missing_keys.append("SERPAPI_API_KEY")
    logging.error("SERPAPI_API_KEY is missing in .env file.")

if not GROQ_API_KEY:
    missing_keys.append("GROQ_API_KEY")
    logging.error("GROQ_API_KEY is missing in .env file.")

if not MODEL_ID:
    missing_keys.append("MODEL_ID")
    logging.error("MODEL_ID for Gemini is missing in .env file.")

if missing_keys:
    keys_str = ", ".join(missing_keys)
    st.error(f"ERROR: Required API keys/config missing in .env: {keys_str}. Application functionality will be limited.")
    logging.critical(f"Missing critical API keys/config: {keys_str}. Halting application.")
    st.stop()
else:
    logging.info("Required API keys and MODEL_ID loaded successfully.")

try:
    model = Gemini(id=MODEL_ID, search=True, temperature=0.3)
    logging.info(f"Gemini model '{MODEL_ID}' initialized successfully.")
except Exception as e:
    st.error(f"Fatal Error: Failed to initialize Gemini model: {e}")
    logging.critical(f"Failed to initialize Gemini model: {e}", exc_info=True)
    st.stop()

if SERPAPI_API_KEY:
    litigation_search_tool = LitigationSearch(serpapi_key=SERPAPI_API_KEY)
else:
    litigation_search_tool = None
    logging.warning("LitigationSearch tool not initialized due to missing SERPAPI_API_KEY.")


@st.cache_data(show_spinner=False, ttl=timedelta(days=1))
def cached_get_patent_full_details(patent_number):
    logging.info(f"Executing cached_get_patent_full_details for {patent_number}")
    return get_patent_full_details(patent_number)


def format_date(date_str):
    if not date_str: return "N/A"
    if isinstance(date_str, datetime): return date_str.strftime("%B %d, %Y")
    formats_to_try = ["%Y-%m-%d", "%B %d, %Y", "%Y%m%d", "%d %B %Y", "%m/%d/%Y", "%Y"]
    for fmt in formats_to_try:
        try:
            return datetime.strptime(str(date_str).strip(), fmt).strftime("%B %d, %Y")
        except ValueError:
            continue
    try:
        date_str_cleaned = str(date_str).split('T')[0]
        return datetime.fromisoformat(date_str_cleaned).strftime("%B %d, %Y")
    except ValueError:
        logging.warning(f"Could not parse date string: '{date_str}' with common formats. Returning as is.")
        return str(date_str)


def parse_date_to_datetime(date_str):
    """Parse date string to datetime object for database storage"""
    if not date_str or date_str == "N/A":
        return None

    if isinstance(date_str, datetime):
        return date_str

    formats_to_try = [
        "%Y-%m-%d", "%B %d, %Y", "%Y%m%d", "%d %B %Y", "%m/%d/%Y",
        "%b %d, %Y", "%d %b %Y", "%Y-%m-%d %H:%M:%S"
    ]

    for fmt in formats_to_try:
        try:
            return datetime.strptime(str(date_str).strip(), fmt)
        except ValueError:
            continue

    try:
        date_str_cleaned = str(date_str).split('T')[0]
        return datetime.fromisoformat(date_str_cleaned)
    except ValueError:
        logging.warning(f"Could not parse date string: '{date_str}' to datetime. Returning None.")
        return None


def migrate_database():
    """Migrate existing database to add user_id columns"""
    conn = sqlite3.connect(WISSEN_DB_PATH)
    cursor = conn.cursor()

    try:
        # Check if user_id column exists in patent_novelty
        cursor.execute("PRAGMA table_info(patent_novelty)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'user_id' not in columns:
            logging.info("Migrating patent_novelty table to add user_id column")

            # Create new table with user_id
            cursor.execute('''
                CREATE TABLE patent_novelty_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    patent_number TEXT NOT NULL,
                    user_id INTEGER NOT NULL DEFAULT 1,
                    title TEXT,
                    priority_date DATETIME,
                    assignees TEXT,
                    competitors TEXT,
                    novelty_summary TEXT,
                    inserted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME,
                    UNIQUE(patent_number, user_id)
                )
            ''')

            # Copy existing data with default user_id = 1
            cursor.execute('''
                INSERT INTO patent_novelty_new
                (id, patent_number, user_id, title, priority_date, assignees, competitors, novelty_summary, inserted_at, updated_at)
                SELECT id, patent_number, 1, title, priority_date, assignees, competitors, novelty_summary, inserted_at, updated_at
                FROM patent_novelty
            ''')

            # Drop old table and rename new one
            cursor.execute('DROP TABLE patent_novelty')
            cursor.execute('ALTER TABLE patent_novelty_new RENAME TO patent_novelty')

            logging.info("Successfully migrated patent_novelty table")

        # Check if user_id column exists in infringed_models
        cursor.execute("PRAGMA table_info(infringed_models)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'user_id' not in columns:
            logging.info("Migrating infringed_models table to add user_id column")

            # Create new table with user_id
            cursor.execute('''
                CREATE TABLE infringed_models_new (
                    id INTEGER NOT NULL,
                    patent_number TEXT NOT NULL,
                    user_id INTEGER NOT NULL DEFAULT 1,
                    priority_date DATETIME,
                    model TEXT NOT NULL,
                    launched_date TEXT,
                    processed_launched_date DATETIME,
                    link TEXT,
                    inserted_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
                    updated_at DATETIME,
                    is_relevant INTEGER,
                    relevance_reason TEXT,
                    UNIQUE(patent_number, model, user_id),
                    PRIMARY KEY(id AUTOINCREMENT)
                )
            ''')

            # Copy existing data with default user_id = 1
            cursor.execute('''
                INSERT INTO infringed_models_new
                (id, patent_number, user_id, priority_date, model, launched_date, processed_launched_date, link, inserted_at, updated_at, is_relevant, relevance_reason)
                SELECT id, patent_number, 1, priority_date, model, launched_date, processed_launched_date, link, inserted_at, updated_at, is_relevant, relevance_reason
                FROM infringed_models
            ''')

            # Drop old table and rename new one
            cursor.execute('DROP TABLE infringed_models')
            cursor.execute('ALTER TABLE infringed_models_new RENAME TO infringed_models')

            logging.info("Successfully migrated infringed_models table")

        conn.commit()

    except Exception as e:
        logging.error(f"Error during database migration: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()


def init_database():
    """Initialize database tables for storing analysis results"""
    conn = sqlite3.connect(WISSEN_DB_PATH)
    cursor = conn.cursor()

    cursor.execute('''
            CREATE TABLE IF NOT EXISTS infringed_models (
                id INTEGER NOT NULL,
                patent_number TEXT NOT NULL,
                user_id INTEGER NOT NULL,
                priority_date DATETIME,
                model TEXT NOT NULL,
                launched_date TEXT,
                processed_launched_date DATETIME,
                link TEXT,
                inserted_at DATETIME DEFAULT (CURRENT_TIMESTAMP),
                updated_at DATETIME,
                is_relevant INTEGER,  -- 1 for relevant, 0 for not relevant, NULL for not set
                relevance_reason TEXT, -- User's reason for relevance assessment
                UNIQUE(patent_number, model, user_id),
                PRIMARY KEY(id AUTOINCREMENT)
            )
    ''')

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS claim_charts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            infringed_model_id INTEGER NOT NULL,
            claim_element TEXT NOT NULL,
            corresponding_feature TEXT, -- Changed from NUMERIC to TEXT
            source_justification TEXT,
            infringement_risk TEXT,
            risk_justification TEXT,
            inserted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (infringed_model_id) REFERENCES infringed_models(id)
        )
    ''')

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS patent_novelty (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patent_number TEXT NOT NULL,
            user_id INTEGER NOT NULL,
            title TEXT,
            priority_date DATETIME,
            assignees TEXT,
            competitors TEXT,
            novelty_summary TEXT,
            inserted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME,
            UNIQUE(patent_number, user_id)
        )
    ''')

    conn.commit()
    conn.close()

    # Run migration after table creation
    migrate_database()

    logging.info("Database tables initialized successfully with user tracking support.")


def check_existing_analysis(patent_number, user_id):
    """Check if patent analysis already exists in database for this user"""
    try:
        conn = sqlite3.connect(WISSEN_DB_PATH)
        cursor = conn.cursor()

        # Check if we have existing analysis for this patent and user
        cursor.execute('''
            SELECT COUNT(*) FROM infringed_models WHERE patent_number = ? AND user_id = ?
        ''', (patent_number, user_id))
        model_count = cursor.fetchone()[0]

        if model_count > 0:
            logging.info(f"Found existing analysis for patent {patent_number} with {model_count} products for user {user_id}")
            return True

        conn.close()
        return False
    except Exception as e:
        logging.error(f"Error checking existing analysis for {patent_number} and user {user_id}: {e}")
        return False


def load_analysis_from_database(patent_number, user_id):
    """Load existing analysis results from database for specific user"""
    try:
        conn = sqlite3.connect(WISSEN_DB_PATH)
        cursor = conn.cursor()

        # Get patent metadata for this user
        cursor.execute('''
            SELECT title, priority_date, assignees, competitors, novelty_summary
            FROM patent_novelty WHERE patent_number = ? AND user_id = ?
        ''', (patent_number, user_id))
        patent_meta = cursor.fetchone()

        # Get phase 1 results (identified products) for this user
        cursor.execute('''
            SELECT model, launched_date, link, is_relevant, relevance_reason FROM infringed_models
            WHERE patent_number = ? AND user_id = ?
        ''', (patent_number, user_id))
        models_data = cursor.fetchall()

        phase1_results = []
        phase2_results = []

        for model_data in models_data:
            model_name = model_data[0]
            launch_date = model_data[1]
            links = model_data[2]
            is_relevant = model_data[3]  # New column
            relevance_reason = model_data[4]  # New column

            # Parse company and model from combined name
            if " - " in model_name:
                company, model = model_name.split(" - ", 1)
            else:
                company = "Unknown"
                model = model_name

            # Convert links string back to list
            link_list = links.split('\n') if links else []

            phase1_results.append({
                'company': company,
                'model': model,
                'launch_date': launch_date,
                'infringement_evidence_links': link_list,
                'is_relevant': is_relevant,  # Add to result
                'relevance_reason': relevance_reason
            })

        # Get phase 2 results (claim charts)
        for model_data in models_data:
            model_name = model_data[0]

            # Parse company and model
            if " - " in model_name:
                company, model = model_name.split(" - ", 1)
            else:
                company = "Unknown"
                model = model_name

            # Get model ID for this user
            cursor.execute('''
                SELECT id FROM infringed_models
                WHERE patent_number = ? AND model = ? AND user_id = ?
            ''', (patent_number, model_name, user_id))
            model_id_result = cursor.fetchone()

            if model_id_result:
                model_id = model_id_result[0]

                # Get claim chart data
                cursor.execute('''
                    SELECT claim_element, corresponding_feature, source_justification, 
                           infringement_risk, risk_justification
                    FROM claim_charts WHERE infringed_model_id = ?
                ''', (model_id,))
                claim_data = cursor.fetchall()

                if claim_data:
                    claim_chart = []
                    infringement_risk = claim_data[0][3] if claim_data else "N/A"
                    risk_justification = claim_data[0][4] if claim_data else "N/A"

                    for claim_row in claim_data:
                        claim_chart.append({
                            'claim_element': claim_row[0],
                            'corresponding_feature': claim_row[1],
                            'source_justification': claim_row[2]
                        })

                    phase2_results.append({
                        'company': company,
                        'model': model,
                        'category': 'N/A',  # Not stored separately
                        'activity_date': 'N/A',  # Not stored separately
                        'infringement_risk': infringement_risk,
                        'risk_justification': risk_justification,
                        'claim_chart': claim_chart,
                        'sources': [],  # Not stored separately
                        'search_queries': []  # Not stored separately
                    })

        conn.close()

        # Extract metadata
        priority_date = "N/A"
        assignees = []
        competitors = []
        novelty_summary = "N/A"

        if patent_meta:
            priority_date = format_date(patent_meta[1]) if patent_meta[1] else "N/A"
            assignees = json.loads(patent_meta[2]) if patent_meta[2] else []
            competitors = json.loads(patent_meta[3]) if patent_meta[3] else []
            novelty_summary = patent_meta[4] if patent_meta[4] else "N/A"

        logging.info(
            f"Loaded existing analysis from database for {patent_number} and user {user_id}: {len(phase1_results)} products, {len(phase2_results)} analyzed")
        return phase1_results, phase2_results, priority_date, competitors, novelty_summary, assignees

    except Exception as e:
        logging.error(f"Error loading analysis from database for {patent_number} and user {user_id}: {e}")
        return None, None, "N/A", [], "Error loading from database", []


def save_to_database(patent_number, priority_date, phase1_results, phase2_results, user_id, title="", assignees=None,
                     competitors=None, novelty_summary=""):
    """Save analysis results to database with user tracking"""
    if not phase1_results and novelty_summary == "Error fetching patent data.":  # Avoid saving if only error
        logging.warning(f"Skipping database save for {patent_number} due to no results and patent fetch error.")
        return False

    if not WISSEN_DB_PATH:
        logging.error("WISSEN_DB_PATH is not set. Cannot save to database.")
        st.error("Database path not configured. Cannot save results.")
        return False

    if not user_id:
        logging.error("User ID is required for saving to database.")
        st.error("User authentication required. Please log in again.")
        return False

    conn = sqlite3.connect(WISSEN_DB_PATH)
    cursor = conn.cursor()

    # Save patent metadata with user_id
    try:
        priority_date_dt = parse_date_to_datetime(priority_date)
        cursor.execute('''
            INSERT OR REPLACE INTO patent_novelty
            (patent_number, user_id, title, priority_date, assignees, competitors, novelty_summary, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (patent_number, user_id, title, priority_date_dt,
              json.dumps(assignees or []), json.dumps(competitors or []),
              novelty_summary, datetime.now()))
        logging.info(f"Saved/Replaced patent_novelty for {patent_number} and user {user_id}")
    except Exception as e:
        logging.error(f"Error saving patent metadata for {patent_number} and user {user_id}: {e}", exc_info=True)
        # Do not close connection here, allow product saving to proceed if possible, or handle as critical error
        # conn.close()
        # return False # Decide if this is a fatal error for the whole save operation

    # Save Phase 1 results (identified products)
    if phase1_results and isinstance(phase1_results, list):
        for product in phase1_results:
            if not isinstance(product, dict):
                logging.warning(f"Skipping non-dictionary product in save_to_database: {type(product)}")
                continue

            company = product.get('company', '')
            model_name_only = product.get('model', '')  # Use 'model' as the model name part
            launch_date = product.get('launch_date', '')
            launch_date_dt = parse_date_to_datetime(launch_date)
            priority_date_dt = parse_date_to_datetime(product.get('priority_date', ''))

            evidence_links = product.get('infringement_evidence_links', [])
            if not isinstance(evidence_links, list):
                evidence_links = [str(evidence_links)]
            link_str = '\n'.join(str(link).strip() for link in evidence_links if link and str(link).strip())

            full_model_name = f"{company} - {model_name_only}" if company and model_name_only else model_name_only
            if not full_model_name:  # Ensure there's a model identifier
                logging.warning(f"Skipping product with empty company and model name for patent {patent_number}")
                continue

            model_id = None
            try:
                # Corrected INSERT OR REPLACE statement for infringed_models with user_id
                cursor.execute('''
                    INSERT OR REPLACE INTO infringed_models
                    (patent_number, user_id, priority_date, model, launched_date, processed_launched_date, link, updated_at, is_relevant, relevance_reason)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (patent_number, user_id, priority_date_dt, full_model_name, launch_date, launch_date_dt, link_str,
                      datetime.now(), product.get('is_relevant', None), product.get('relevance_reason', '')))

                logging.info(
                    f"Saved/Replaced infringed_model: {full_model_name} for patent {patent_number}. Rowcount: {cursor.rowcount}")

                # Get the ID of the inserted/replaced row
                # If it was an INSERT, lastrowid works.
                # If it was a REPLACE (DELETE then INSERT), lastrowid also works for the new row.
                # If INSERT OR IGNORE and it was ignored, lastrowid is not updated.
                # Since it's INSERT OR REPLACE, a row is always affected if no error.
                if cursor.rowcount > 0:
                    model_id = cursor.lastrowid
                    # If lastrowid is 0 or None (can happen with "REPLACE" if the rowid was 0, or some drivers), query it.
                    if not model_id:
                        cursor.execute('SELECT id FROM infringed_models WHERE patent_number = ? AND model = ? AND user_id = ?',
                                       (patent_number, full_model_name, user_id))
                        result = cursor.fetchone()
                        if result:
                            model_id = result[0]
                else:  # Should not happen with INSERT OR REPLACE unless an error occurred before, or data is identical and no change
                    cursor.execute('SELECT id FROM infringed_models WHERE patent_number = ? AND model = ? AND user_id = ?',
                                   (patent_number, full_model_name, user_id))
                    result = cursor.fetchone()
                    if result:
                        model_id = result[0]
                        logging.info(
                            f"Retrieved existing model_id {model_id} for {full_model_name} as rowcount was 0 after REPLACE attempt.")
                    else:
                        logging.error(
                            f"Could not obtain model_id for {full_model_name} after INSERT OR REPLACE attempt for patent {patent_number} and user {user_id}.")
                        continue  # Skip to next product if model_id is not found

                if not model_id:
                    logging.error(
                        f"Failed to obtain a valid model_id for {full_model_name} (Patent: {patent_number}). Skipping claim chart save for this model.")
                    continue

            except sqlite3.Error as e_model:  # Catch SQLite specific errors
                logging.error(f"SQLite error saving product {full_model_name} for patent {patent_number}: {e_model}",
                              exc_info=True)
                continue  # Skip to next product
            except Exception as e_general:
                logging.error(f"General error saving product {full_model_name} for patent {patent_number}: {e_general}",
                              exc_info=True)
                continue

            # Save Phase 2 results (claim charts) for this specific model_id
            if model_id and phase2_results and isinstance(phase2_results, list):
                matching_analysis = None
                for p2_item in phase2_results:
                    if not isinstance(p2_item, dict):
                        continue

                    p2_company = p2_item.get('company', '').strip().lower()
                    p2_model = p2_item.get('model', '').strip().lower()

                    # Match against the company and model_name_only from phase1_results
                    if (p2_company == company.strip().lower() and
                            p2_model == model_name_only.strip().lower()):
                        matching_analysis = p2_item
                        break

                if matching_analysis:
                    # Before saving new claim charts, delete existing ones for this model_id to prevent duplicates on re-runs
                    try:
                        cursor.execute("DELETE FROM claim_charts WHERE infringed_model_id = ?", (model_id,))
                        logging.info(f"Deleted existing claim_charts for model_id {model_id} before new insert.")
                    except Exception as e_delete:
                        logging.error(f"Error deleting old claim charts for model_id {model_id}: {e_delete}",
                                      exc_info=True)

                    claim_chart_list = matching_analysis.get('claim_chart', [])
                    if not isinstance(claim_chart_list, list):
                        claim_chart_list = []

                    infringement_risk = matching_analysis.get('infringement_risk', 'Unknown')
                    risk_justification = matching_analysis.get('risk_justification',
                                                               matching_analysis.get('infringement_risk_explanation',
                                                                                     ''))

                    if claim_chart_list and not (
                            len(claim_chart_list) == 1 and claim_chart_list[0].get('claim_element') == "Error"):
                        for claim_entry in claim_chart_list:
                            if not isinstance(claim_entry, dict):
                                continue

                            try:
                                cursor.execute('''
                                    INSERT INTO claim_charts 
                                    (infringed_model_id, claim_element, corresponding_feature, source_justification, 
                                     infringement_risk, risk_justification)
                                    VALUES (?, ?, ?, ?, ?, ?)
                                ''', (model_id, claim_entry.get('claim_element', ''),
                                      claim_entry.get('corresponding_feature',
                                                      claim_entry.get('product_element', '')),
                                      claim_entry.get('source_justification',
                                                      claim_entry.get('match_explanation', '')),
                                      infringement_risk, risk_justification))
                            except sqlite3.Error as e_chart:
                                logging.error(
                                    f"SQLite error saving claim chart entry for model_id {model_id} ({full_model_name}): {e_chart}",
                                    exc_info=True)
                            except Exception as e_general_chart:
                                logging.error(
                                    f"General error saving claim chart entry for model_id {model_id} ({full_model_name}): {e_general_chart}",
                                    exc_info=True)
                        logging.info(
                            f"Saved {len(claim_chart_list)} claim_chart entries for model_id {model_id} ({full_model_name}).")
    else:
        logging.info(f"No Phase 1 results to save for patent {patent_number}.")

    try:
        conn.commit()
        logging.info(f"Successfully committed database changes for patent {patent_number}.")
    except Exception as e_commit:
        logging.error(f"Error committing changes to database for patent {patent_number}: {e_commit}", exc_info=True)
        conn.close()
        return False

    conn.close()
    return True


def save_feedback_to_database(patent_number, products, user_id):
    """Save user feedback to database with user tracking"""
    try:
        if not user_id:
            logging.error("User ID is required for saving feedback.")
            return False

        conn = sqlite3.connect(WISSEN_DB_PATH)
        cursor = conn.cursor()

        for product in products:
            if not isinstance(product, dict):
                continue

            company = product.get('company', '')
            model_name_only = product.get('model', '')
            full_model_name = f"{company} - {model_name_only}" if company and model_name_only else model_name_only

            cursor.execute('''
                UPDATE infringed_models
                SET is_relevant = ?, relevance_reason = ?, updated_at = ?
                WHERE patent_number = ? AND model = ? AND user_id = ?
            ''', (
                product.get('is_relevant'),
                product.get('relevance_reason'),
                datetime.now(),
                patent_number,
                full_model_name,
                user_id
            ))

        conn.commit()
        logging.info(f"Saved feedback for {len(products)} products for patent {patent_number} and user {user_id}")
        return True

    except Exception as e:
        logging.error(f"Error saving feedback to database: {e}", exc_info=True)
        return False
    finally:
        if conn:
            conn.close()

def extract_json_from_text(text):
    """Enhanced JSON extraction with multiple fallback strategies"""
    if not text or not isinstance(text, str):
        return None

    # Strategy 1: Look for JSON code blocks
    json_patterns = [
        r'```json\s*(.*?)\s*```',
        r'```\s*(.*?)\s*```',
        r'`(.*?)`'
    ]

    for pattern in json_patterns:
        match = re.search(pattern, text, re.DOTALL | re.MULTILINE)
        if match:
            try:
                json_str = match.group(1).strip()
                return json.loads(json_str)
            except json.JSONDecodeError:
                continue

    # Strategy 2: Look for JSON arrays or objects in the text
    json_object_patterns = [
        r'(\[.*?\])',
        r'(\{.*?\})'
    ]

    for pattern in json_object_patterns:
        matches = re.findall(pattern, text, re.DOTALL)
        for match in matches:
            try:
                parsed = json.loads(match.strip())
                if isinstance(parsed, (list, dict)):
                    return parsed
            except json.JSONDecodeError:
                continue

    # Strategy 3: Try to parse the entire text as JSON
    try:
        return json.loads(text.strip())
    except json.JSONDecodeError:
        pass

    # Strategy 4: Look for JSON-like structures and attempt to fix common issues
    cleaned_text = text.strip()

    # Remove common non-JSON prefixes/suffixes
    prefixes_to_remove = [
        "Here's the analysis:",
        "Based on the analysis:",
        "The result is:",
        "Analysis result:",
        "Here is the JSON:",
        "JSON response:",
    ]

    for prefix in prefixes_to_remove:
        if cleaned_text.lower().startswith(prefix.lower()):
            cleaned_text = cleaned_text[len(prefix):].strip()

    # Try parsing again after cleanup
    try:
        return json.loads(cleaned_text)
    except json.JSONDecodeError:
        pass

    # If all strategies fail, return None to trigger error handling
    logging.warning("JSON parsing failed, all extraction strategies exhausted")
    return None


def create_excel_analysis(phase1_data, phase2_data):
    """Creates an Excel file buffer containing Phase 1 and Phase 2 analysis."""
    output_buffer = BytesIO()
    with pd.ExcelWriter(output_buffer, engine='openpyxl') as writer:
        if phase1_data:
            df_phase1_data = []
            for item in phase1_data:
                # Convert string to dictionary if needed
                if isinstance(item, str):
                    try:
                        item = json.loads(item)
                    except json.JSONDecodeError:
                        logging.warning(f"Skipping non-JSON string item in phase1_data: {item[:100]}...")
                        continue

                # Skip if not a dictionary
                if not isinstance(item, dict):
                    logging.warning(f"Skipping non-dictionary product in create_excel_analysis: {type(item)}")
                    continue

                links = item.get('infringement_evidence_links', [])
                df_phase1_data.append({
                    'company': item.get('company'),
                    'model': item.get('model'),
                    'launch_date': item.get('launch_date'),
                    'infringement_evidence_links': "\n".join(links) if isinstance(links, list) else links,
                    'is_relevant': "Relevant" if item.get('is_relevant') == 1 else ("Not Relevant" if item.get('is_relevant') == 0 else "Not Rated"),  # New column
                    'relevance_reason': item.get('relevance_reason', '')  # New column
                })

            df_phase1 = pd.DataFrame(df_phase1_data)
            df_phase1.rename(columns={
                'company': 'Company',
                'model': 'Product/Model',
                'launch_date': 'Launch Date',
                'infringement_evidence_links': 'Initial Evidence Links',
                'is_relevant': 'Relevance Status',
                'relevance_reason': 'Relevance Reason'
            }, inplace=True)

            df_phase1['Launch Date'] = df_phase1['Launch Date'].apply(format_date)
            df_phase1.to_excel(writer, sheet_name='Identified Products', index=False)
            worksheet = writer.sheets['Identified Products']
            for column_cells in worksheet.columns:
                max_length = 0
                column_letter = column_cells[0].column_letter
                for cell in column_cells:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2) * 1.2
                worksheet.column_dimensions[column_letter].width = min(adjusted_width, 70)
        else:
            pd.DataFrame({"Status": ["No products identified in Phase 1"]}).to_excel(writer,
                                                                                     sheet_name='Identified Products',
                                                                                     index=False)

        if phase2_data:
            for product_analysis in phase2_data:
                # Convert string to dictionary if needed
                if isinstance(product_analysis, str):
                    try:
                        product_analysis = json.loads(product_analysis)
                    except json.JSONDecodeError:
                        logging.warning(f"Skipping non-JSON string item in phase2_data: {product_analysis[:100]}...")
                        continue

                # Skip if not a dictionary
                if not isinstance(product_analysis, dict):
                    logging.warning(f"Skipping non-dictionary product in phase2_data: {type(product_analysis)}")
                    continue

                company = product_analysis.get('company', 'Unknown Company')
                model_name = product_analysis.get('model', 'Unknown Model')
                safe_model_name = re.sub(r'[\\/*?:\[\]]', '', model_name)[:25].strip()
                sheet_name = f"Chart - {safe_model_name}" if safe_model_name else "Chart - Unknown"
                original_sheet_name = sheet_name
                count = 1
                while sheet_name in writer.sheets:
                    sheet_name = f"{original_sheet_name[:28 - len(str(count))]}_{count}"
                    count += 1

                claim_chart_list = product_analysis.get('claim_chart', [])
                if claim_chart_list and not (len(claim_chart_list) == 1 and claim_chart_list[0].get(
                        'claim_element') == "Error"):
                    df_chart = pd.DataFrame(claim_chart_list)
                    df_chart.rename(columns={
                        'claim_element': 'Patent Claim Element',
                        'corresponding_feature': 'Corresponding Product Feature/Step',
                        'source_justification': 'Source(s) & Justification'
                    }, inplace=True)
                else:
                    status_message = "No claim chart data available for this product."
                    if claim_chart_list and claim_chart_list[0].get('claim_element') == "Error":
                        status_message = product_analysis.get('risk_justification',
                                                              "Claim chart generation failed for this product.")
                    df_chart = pd.DataFrame({"Status": [status_message]})

                metadata_rows = [
                    ("Company:", company),
                    ("Product/Model:", model_name),
                    ("Category:", product_analysis.get('category', 'N/A')),
                    ("Activity Date:", format_date(product_analysis.get('activity_date', 'N/A'))),
                    ("Infringement Risk:",
                     f"{product_analysis.get('infringement_risk', 'N/A')} - {product_analysis.get('risk_justification', 'N/A')}"),
                    ("Sources Used:",
                     "\n".join(product_analysis.get('sources', ['N/A'])) if isinstance(product_analysis.get('sources'),
                                                                                       list) else product_analysis.get(
                         'sources', 'N/A')),
                    ("Search Queries:", "\n".join(product_analysis.get('search_queries', ['N/A'])) if isinstance(
                        product_analysis.get('search_queries'), list) else product_analysis.get('search_queries',
                                                                                                'N/A'))
                ]
                df_metadata = pd.DataFrame(metadata_rows)
                df_metadata.to_excel(writer, sheet_name=sheet_name, index=False, header=False, startrow=0)

                df_chart.to_excel(writer, sheet_name=sheet_name, index=False, startrow=len(df_metadata) + 1)

                worksheet_chart = writer.sheets[sheet_name]

                for row_num in range(1, len(df_metadata) + 1):
                    cell_label = worksheet_chart.cell(row=row_num, column=1)
                    cell_label.font = cell_label.font.copy(bold=True)
                    cell_value = worksheet_chart.cell(row=row_num, column=2)
                    cell_value.alignment = cell_value.alignment.copy(wrapText=True, vertical='top')
                worksheet_chart.column_dimensions['A'].width = 20
                worksheet_chart.column_dimensions['B'].width = 80

                header_row_idx = len(df_metadata) + 2
                for col_idx, column_name_excel in enumerate(df_chart.columns):
                    col_letter = chr(ord('A') + col_idx)
                    header_cell = worksheet_chart.cell(row=header_row_idx, column=col_idx + 1)
                    header_cell.font = header_cell.font.copy(bold=True)

                    max_len_header = len(str(column_name_excel))
                    max_len_data = 0
                    if not df_chart.empty and column_name_excel in df_chart:
                        max_len_data = df_chart[column_name_excel].astype(str).map(len).max()

                    max_len = max(max_len_header, max_len_data if pd.notna(max_len_data) else 0)

                    wrap_columns = ['Patent Claim Element', 'Corresponding Product Feature/Step',
                                    'Source(s) & Justification', 'Status']
                    if column_name_excel in wrap_columns:
                        width = 60
                        worksheet_chart.column_dimensions[col_letter].width = width
                        for r_idx in range(header_row_idx + 1, header_row_idx + 1 + len(df_chart)):
                            cell = worksheet_chart.cell(row=r_idx, column=col_idx + 1)
                            cell.alignment = cell.alignment.copy(wrapText=True, vertical='top')
                    else:
                        width = min((max_len + 5) * 1.2, 60)
                        worksheet_chart.column_dimensions[col_letter].width = width
        elif not phase1_data:
            pass
        else:
            pd.DataFrame({"Status": [
                "Claim chart analysis (Phase 2) was skipped or failed, and no Phase 1 products were identified."]}).to_excel(
                writer, sheet_name='Claim Chart Status', index=False)
    output_buffer.seek(0)
    return output_buffer


def init_agents():
    """Initializes and returns all agents."""
    novelty_agent_instructions = f"""
            **Objective:** Analyze the provided patent information (title, abstract, and independent claims) to identify and summarize its core novelty and distinguishing technical features.

            **Input:**
            1.  `patent_number` (String): The patent number for context.
            2.  `patent_title` (String): The title of the patent.
            3.  `patent_abstract` (String): The abstract of the patent.
            4.  `independent_claims` (List of Strings): The text of the patent's independent claims.

            **Tasks:**
            1.  **Understand Overall Invention:** Read the title and abstract to get a general understanding of the invention's field and purpose.
            2.  **Detailed Claim Analysis:** Carefully examine each independent claim to identify the essential elements and their combination.
            3.  **Identify Core Novelty:** Determine the key technical contribution or the specific problem solved by the invention in a new way, as indicated by the claims in light of the abstract and title. Focus on what appears to be the inventive step or the unique combination of features.
            4.  **Summarize Novelty:** Produce a concise summary (2-4 sentences) of this core novelty. The summary should be clear, technically accurate, and highlight the distinguishing aspects. Avoid jargon where possible or explain it briefly.

            **Output Format:** Return ONLY a plain text string containing the novelty summary. Do not include any other explanatory text, titles, or markdown formatting like "```".
            """

    novelty_analysis_agent = Agent(
        model=model,
        markdown=False,
        description="Expert in analyzing patent documents to extract and summarize core novelty.",
        instructions=[novelty_agent_instructions],
        show_tool_calls=True,
    )

    product_finder_agent_instructions = f"""
            **Objective:** Identify commercial products or services that potentially implement the core novel concept of the given patent and were launched *after* its priority date. Conduct exhaustive searches across multiple channels including major e-commerce platforms, big box retailers, and industry-specific sources to identify 6-12 highly relevant products that meet all criteria.

            **Input:**
            1.  `Patent Novelty Summary` (String): A summary of the patent's core inventive concept. THIS IS YOUR PRIMARY GUIDE for identifying relevance.
            2.  `Claim Text` (String): The text of one representative independent patent claim (for detailed feature reference).
            3.  `Priority Date` (String): The critical date; products launched before this are not relevant.
            4.  `Known Competitors` (List of Strings): Company names to investigate first.
            5.  `Assignee Companies` (List of Strings): Companies who own/assigned the patent. **DO NOT list products from these companies.**

            **Enhanced Search Strategy (CRITICAL - Follow All Steps):**
            *   **Multi-Channel Product Discovery:**
                1.  **E-commerce Platforms:** Search Amazon, eBay, Newegg, Best Buy, Target, Walmart, Home Depot, Lowe's for product listings
                2.  **Big Box Retailers:** Best Buy, Target, Walmart, Costco, Sam's Club, Menard's, Staples
                3.  **Industry-Specific Stores:** Hardware stores (Home Depot, Lowe's), electronics stores (Micro Center, B&H), sporting goods (Dick's, REI), automotive (AutoZone, O'Reilly's)
                4.  **Professional/Commercial:** Grainger, McMaster-Carr, Newark, Digi-Key for industrial/technical products
                5.  **Category-Specific Research:** Based on patent novelty, search relevant industry sites and catalogs

            *   **Enhanced Search Terms Strategy:**
                - Combine patent novelty keywords with "product", "device", "system", "solution"
                - Use technical terms from claims with commercial product names
                - Search for "[novelty concept] product launch [year after priority date]"
                - Include model numbers, product families, and technical specifications
                - Search company press releases and product announcements post-priority date

            **Data Sourcing and Evidence Link Guidelines (UNCHANGED - STILL CRITICAL):**
            *   **ALLOWED PRIMARY SOURCES (Your *ONLY* sources for `infringement_evidence_links`):**
                1.  Official Company Product Pages (manufacturer's primary corporate domain).
                2.  Official Company Datasheets/Specifications (on manufacturer's primary corporate domain).
                3.  Official Company User Manuals (on manufacturer's primary corporate domain).
                4.  Official Company Press Releases/News (on the company's *own* newsroom/press section).
                5.  Official Company Blog Posts (technical/product blogs on company's primary corporate domain).
                6.  FCC Filings (Direct Links to `fcc.gov`, `fcc.io`, `fcc.report`).
                7.  Product Manuals/Documentation (hosted on the company's primary corporate domain).

            *   **STRICTLY FORBIDDEN SOURCES for `infringement_evidence_links`:**
                *   Third-party reseller/e-commerce sites (Amazon, BestBuy, Newegg, etc.).
                *   News Aggregators/Press Release Distribution Services.
                *   General Third-Party News Articles/Blogs/Review Sites.

            **Enhanced Product Selection Criteria:**
            *   Target 6-12 products that are most relevant to the patent novelty. Ensure all the products infringe either the novelty aspect or the claims of the given patent
            *   Prioritize products with strong technical alignment to patent novelty or claims
            *   Include products from both major manufacturers and smaller specialized companies
            *   Ensure geographic diversity matching patent jurisdiction
            *   Focus on products with clear post-priority-date launch evidence
            *   Exclude products from assignee companies
            *   Prioritize physical products over software-only solutions. If the patent is direct towards software then prefer software products else mix of both good with more priority to physical products.

            **Tasks:**
            1.  **Multi-Channel Search:** Execute comprehensive searches across all mentioned channels
            2.  **Novelty-Focused Filtering:** Ensure all identified products strongly align with the Patent Novelty Summary
            3.  **Compliant Evidence Collection:** For each product, gather URLs ONLY from ALLOWED PRIMARY SOURCES
            4.  **Enhanced Due Diligence:** Verify launch dates, technical specifications, and feature alignment
            5.  **Quality Over Quantity:** Better to have 6-12 high-quality matches than 25 weak ones
            6.  **Explcitly search for Adobe, Byte Dance & Google products.


            **Output Format:** **Return ONLY a JSON list of objects.** Each object:
            *   `company` (String) -  Must NOT be one of the `Assignee Companies`.
            *   `model` (String)
            *   `launch_date` (String)
            *   `infringement_evidence_links` (List of Strings from ALLOWED PRIMARY SOURCES)

            **Critical Constraints:**
            *   **Assignee Exclusion:** Absolutely no products from companies listed in `Assignee Companies`.
            *   **Novelty Focus:** Your primary goal is to find products matching the `Patent Novelty Summary`.
            *   **Source Adherence:** `infringement_evidence_links` MUST be from ALLOWED PRIMARY SOURCES.
            *   **Minimum Target:** Aim for 6-12 qualifying products to ensure comprehensive coverage.
            *   JSON Only: Return `[]` if no qualifying products are found.
            """

    product_finder_agent = Agent(
        model=model,
        markdown=True,
        description="Expert in finding commercial products relevant to patent novelty using comprehensive multi-channel search and official sources.",
        instructions=[product_finder_agent_instructions],
        show_tool_calls=True,
    )

    claim_chart_agent_instructions = f"""
            **Objective:** For each product identified in Phase 1, conduct a thorough and detailed analysis against ALL of the patent's independent claims to create a comprehensive claim chart and assess infringement risk. Focus on finding positive evidence of infringement rather than gaps.

            **CRITICAL OUTPUT FORMAT REQUIREMENT:**
            You MUST return your response in this EXACT JSON format:
            ```json
            [{{
                "company": "Company Name",
                "model": "Product Model",
                "category": "Product Category",
                "activity_date": "YYYY-MM-DD or formatted date",
                "infringement_risk": "High/Medium/Low/Error",
                "risk_justification": "Detailed explanation of Infringement Risk",
                "claim_chart": [
                    {{
                        "claim_element": "First claim element text",
                        "corresponding_feature": "Product feature description",
                        "source_justification": "Source citation and explanation"
                    }},
                    {{
                        "claim_element": "Second claim element text", 
                        "corresponding_feature": "Product feature description",
                        "source_justification": "Source citation and explanation"
                    }}
                ],
                "sources": ["source1", "source2"],
                "search_queries": ["query1", "query2"]
            }}]
            ```

            **IMPORTANT FORMATTING RULES:**
            1. Return ONLY valid JSON. No explanations before or after the JSON.
            2. Start with '[' and end with ']'.
            3. Ensure all keys are in double quotes.
            4. Escape any double quotes within string values.
            5. Do not use single quotes for JSON keys or values.
            6. Ensure all JSON objects have the required fields.
            7. If a field is unknown, use empty strings or arrays rather than omitting the field.

            **Enhanced Analysis Approach:**
            *   **Deep Technical Investigation:** Spend significant time researching each product's technical specifications, architecture, and implementation details
            *   **Claim Element Mapping:** For each claim element, actively search for corresponding product features rather than quickly concluding "not found"
            *   **Multiple Source Cross-Reference:** Use multiple official sources to build a complete picture of product functionality
            *   **Inference from Available Data:** When direct evidence isn't available, make reasonable technical inferences based on product category and official specifications

            **Input:**
            1.  **Patent Details:**
                *   `Priority Date`: The critical date.
                *   `Independent Claims`: A JSON list of the patent's independent claims (text for each). **You must analyze ALL claims provided.**
            2.  **Identified Products List:** A JSON list from Phase 1, containing product details and evidence links.

            **Enhanced Data Sourcing Strategy:**
            *   **MANDATORY SOURCE RESTRICTION (UNCHANGED):**  You **MUST ONLY** use these sources:
                *   Official company websites (product pages from primary corporate domain)
                *   Official product datasheets and specifications
                *   Official user manuals and technical documentation
                *   FCC filings (fcc.gov, fcc.io, fcc.report)
                *   Official company press releases and technical blogs
            """

    claim_chart_agent = Agent(
        model=model,
        markdown=True,
        description="Expert in creating detailed patent claim charts by analyzing products against claims.",
        instructions=[claim_chart_agent_instructions],
        show_tool_calls=True,
    )

    logging.info("All agents initialized with updated instructions.")
    return novelty_analysis_agent, product_finder_agent, claim_chart_agent


def analyze_patent(patent_number):
    # Get user_id from session state
    user_id = st.session_state.get("user_id")
    if not user_id:
        st.error("User authentication required. Please log in again.")
        return None, None, "N/A", [], "Authentication required.", []

    # Check if analysis already exists in database for this user
    if check_existing_analysis(patent_number, user_id):
        st.info(f"Found existing analysis for patent {patent_number} in database. Loading saved results...")
        logging.info(f"Loading existing analysis for {patent_number} from database for user {user_id}")

        phase1_results, phase2_results, priority_date, competitors, novelty_summary, assignees = load_analysis_from_database(
            patent_number, user_id)

        if phase1_results is not None:
            st.success(f"Successfully loaded existing analysis with {len(phase1_results)} products from database.")
            return phase1_results, phase2_results, priority_date, competitors, novelty_summary, assignees
        else:
            st.warning("Failed to load existing analysis from database. Proceeding with fresh analysis...")

    novelty_analysis_agent, product_finder_agent, claim_chart_agent = init_agents()

    patent_data = cached_get_patent_full_details(patent_number)

    if patent_data.get("error"):
        st.error(f"Failed to fetch patent details for {patent_number}: {patent_data['error']}")
        return None, None, "N/A", [], "Error fetching patent data.", []

    assignees = patent_data.get("assignees", [])
    independent_claims = patent_data.get("independent_claims", [])
    priority_date_raw = patent_data.get("priority_date")
    patent_title = patent_data.get("title", "N/A")
    patent_abstract = patent_data.get("abstract", "N/A")
    forward_citation_assignees_list = patent_data.get("forward_citation_assignees", [])
    patent_jurisdiction = patent_data.get("country_code", "US")

    phase1_results, phase2_results = None, None
    competitors = []
    formatted_priority_date = "N/A"
    novelty_summary = "Novelty analysis not performed or failed."

    try:
        if not independent_claims:
            raise ValueError("Could not retrieve independent claims text.")
        if not priority_date_raw:
            raise ValueError("Could not retrieve priority date.")

        formatted_priority_date = format_date(priority_date_raw)
        logging.info(
            f"Patent {patent_number}: Assignees: {assignees}, Claims Count: {len(independent_claims)}, Priority Date: {formatted_priority_date}, Title: {patent_title}, Jurisdiction: {patent_jurisdiction}")

    except ValueError as ve:
        logging.error(f"Data validation error for {patent_number}: {ve}", exc_info=True)
        st.error(f"Error processing patent details: {ve}")
        return None, None, formatted_priority_date, [], novelty_summary, assignees

    st.info(f"Analyzing Patent: {patent_number} - {patent_title}")
    st.write(f"**Assignee(s):** {', '.join(assignees) if assignees else 'N/A'}")
    st.write(f"**Priority Date:** {formatted_priority_date}")
    st.write(f"**Number of Independent Claims:** {len(independent_claims)}")
    if patent_abstract and patent_abstract != "N/A":
        with st.expander("Patent Abstract"):
            st.markdown(patent_abstract)

    with st.status("Phase 0: Analyzing Patent Novelty...", expanded=True) as status0:
        try:
            novelty_input_payload = {
                "patent_number": patent_number,
                "patent_title": patent_title if patent_title else "Not Available",
                "patent_abstract": patent_abstract if patent_abstract else "Not Available",
                "independent_claims": independent_claims
            }
            novelty_query = (
                f"Please analyze the following patent to determine its core novelty.\n"
                f"Patent Number: {novelty_input_payload['patent_number']}\n"
                f"Title: {novelty_input_payload['patent_title']}\n"
                f"Abstract: {novelty_input_payload['patent_abstract']}\n"
                f"Independent Claims:\n```json\n{json.dumps(novelty_input_payload['independent_claims'], indent=2)}\n```\n"
                f"Based on your instructions, provide ONLY the plain text novelty summary."
            )
            logging.info(f"Novelty Agent Query for {patent_number} (approx length {len(novelty_query)} chars)")

            novelty_run = novelty_analysis_agent.run(novelty_query)

            if novelty_run and novelty_run.content:
                extracted_summary = novelty_run.content.strip()
                extracted_summary = re.sub(r"^```(?:text|plain)?\s*", "", extracted_summary, flags=re.IGNORECASE)
                extracted_summary = re.sub(r"\s*```$", "", extracted_summary)
                novelty_summary = extracted_summary.strip()

                if not novelty_summary:
                    novelty_summary = "Novelty agent returned an empty summary."
                    logging.warning(f"Novelty extraction returned an empty summary for {patent_number}.")
                    status0.update(label="Novelty Analysis: Empty summary returned.", state="warning")
                else:
                    logging.info(f"Novelty extracted for {patent_number}: {novelty_summary}")
                    status0.update(label="Patent Novelty Analysis Complete.", state="complete")
            else:
                novelty_summary = "Novelty agent returned no content."
                logging.warning(f"Novelty extraction returned no content for {patent_number}.")
                status0.update(label="Novelty Analysis: No content returned.", state="warning")
        except Exception as e:
            novelty_summary = f"Error during novelty extraction: {str(e)[:100]}..."
            logging.error(f"Error during novelty extraction for {patent_number}: {e}", exc_info=True)
            status0.update(label="Novelty Analysis Error.", state="error")

    st.session_state.patent_novelty_summary = novelty_summary

    with st.spinner("Identifying potential target companies..."):
        if litigation_search_tool:
            try:
                litigation_competitors = litigation_search_tool.get_competitors_from_patent(patent_number, assignees)
                if litigation_competitors:
                    competitors.extend(litigation_competitors)
                    logging.info(
                        f"Found {len(litigation_competitors)} competitor(s) via litigation: {litigation_competitors}")
                else:
                    logging.info(f"No direct competitors found via litigation for {patent_number}.")
            except Exception as e:
                logging.warning(f"Litigation search failed for {patent_number}: {e}", exc_info=True)
                st.warning(f"Litigation search encountered an issue: {e}")
        else:
            logging.info("Litigation search tool not available.")

        if forward_citation_assignees_list:
            competitors.extend(forward_citation_assignees_list)
            logging.info(
                f"Found {len(forward_citation_assignees_list)} potential competitor(s) from forward citations: {forward_citation_assignees_list}")
        else:
            logging.info(f"No assignees found from forward citations for {patent_number}.")

    if competitors:
        normalized_assignees = set()
        for assignee_name in assignees:
            if assignee_name and isinstance(assignee_name, str):
                norm_name = assignee_name.lower()
                norm_name = re.sub(r'\b(licensing|inc|llc|ltd|corp|corporation|gmbh|bv|ag|plc|co)\b\.?', '', norm_name,
                                   flags=re.IGNORECASE)
                norm_name = re.sub(r'[,.]', '', norm_name)
                norm_name = norm_name.strip()
                if norm_name:
                    normalized_assignees.add(norm_name)
        logging.info(f"Normalized assignees for filtering: {normalized_assignees}")

        filtered_competitors = []
        raw_competitor_names = set(c.strip() for c in competitors if c and isinstance(c, str) and c.strip())

        for comp_name in raw_competitor_names:
            norm_comp_name = comp_name.lower()
            norm_comp_name = re.sub(r'\b(licensing|inc|llc|ltd|corp|corporation|gmbh|bv|ag|plc|co)\b\.?', '',
                                    norm_comp_name, flags=re.IGNORECASE)
            norm_comp_name = re.sub(r'[,.]', '', norm_comp_name)
            norm_comp_name = norm_comp_name.strip()
            is_assignee_variant = False
            if norm_comp_name and norm_comp_name in normalized_assignees:
                is_assignee_variant = True
            if not is_assignee_variant:
                filtered_competitors.append(comp_name)
        competitors = sorted(list(set(filtered_competitors)))
        if competitors:
            st.success(f"Identified {len(competitors)} potential target companies: {', '.join(competitors)}")
            logging.info(
                f"Final list of {len(competitors)} potential target companies for {patent_number}: {competitors}")
        else:
            st.info("No potential target companies identified from combined searches after filtering.")
            logging.info(f"No potential target companies identified for {patent_number} after filtering.")
    else:
        st.info("No potential target companies identified from litigation or forward citations.")
        logging.info(f"No competitors identified for {patent_number} from any source.")

    claim_for_product_finding = independent_claims[0] if independent_claims else ""
    if not claim_for_product_finding:
        st.error("No independent claim text available for product finding (Phase 1).")
        logging.error(f"Phase 1 cannot proceed for {patent_number}: No independent claim text.")
        return None, None, formatted_priority_date, competitors, novelty_summary, assignees

    phase1_success = False
    with st.status("Phase 1: Identifying relevant products with enhanced multi-channel search...",
                   expanded=True) as status1:
        logging.info(
            f"Starting Phase 1 for {patent_number}. Priority Date: {formatted_priority_date}. Known Competitors: {competitors}. Assignees to Exclude: {assignees}.")

        finder_input_payload = {
            "Patent Novelty Summary": novelty_summary,
            "Claim Text": claim_for_product_finding,
            "Priority Date": formatted_priority_date,
            "Known Competitors": competitors,
            "Assignee Companies": assignees,
            "Patent Jurisdiction": patent_jurisdiction
        }
        finder_query = (
            f"Based on the following details, execute your enhanced multi-channel search instructions and return ONLY the specified JSON list:\n\n"
            f"**Patent Novelty Summary (PRIMARY GUIDE for product relevance):**\n{finder_input_payload['Patent Novelty Summary']}\n\n"
            f"**Claim Text (Reference for specific limitations):**\n```\n{finder_input_payload['Claim Text']}\n```\n\n"
            f"**Priority Date:** {finder_input_payload['Priority Date']}\n\n"
            f"**Patent Jurisdiction:** {finder_input_payload['Patent Jurisdiction']}\n\n"
            f"**Known Competitors (Prioritize these):** {json.dumps(finder_input_payload['Known Competitors']) if finder_input_payload['Known Competitors'] else 'None listed'}\n"
            f"**Assignee Companies (EXCLUDE these):** {json.dumps(finder_input_payload['Assignee Companies']) if finder_input_payload['Assignee Companies'] else 'None listed'}\n\n"
            f"**TARGET: Identify 6-12 highly relevant products using enhanced multi-channel search strategy.**"
        )
        logging.info(f"Phase 1 Agent Query for {patent_number} (approx length {len(finder_query)} chars)")

        try:
            finder_run = product_finder_agent.run(finder_query)
            if finder_run and finder_run.content:
                raw_json_p1 = finder_run.content
                logging.debug(f"Phase 1 Raw Output for {patent_number}: {raw_json_p1}")

                # Use enhanced JSON extraction
                parsed_data = extract_json_from_text(raw_json_p1)

                if parsed_data and isinstance(parsed_data, list):
                    phase1_results = parsed_data
                    phase1_success = True
                    logging.info(
                        f"Phase 1 Success for {patent_number}: Parsed {len(phase1_results)} products from agent output.")
                    status1.update(label=f"Phase 1 Complete: {len(phase1_results)} product(s) identified.",
                                   state="complete")
                elif parsed_data is not None:
                    logging.error(f"Phase 1 Parsed data for {patent_number} is not a list. Type: {type(parsed_data)}.")
                    raise ValueError("Parsed data is not a list.")
                else:
                    logging.warning(f"Phase 1: Could not extract valid JSON for {patent_number}.")
                    phase1_results = []
                    phase1_success = True
                    status1.update(label="Phase 1 Complete: No products identified (invalid JSON response).",
                                   state="complete")

            else:
                st.warning("Product identification (Phase 1) returned no content or failed.")
                phase1_results = []
                phase1_success = True
                status1.update(label="Phase 1 Complete: No products identified.", state="complete")
                logging.warning(f"Phase 1 Warning for {patent_number}: Agent returned no content.")

        except Exception as e:
            logging.error(f"Phase 1 Error for {patent_number}: {e}", exc_info=True)
            st.error(f"Phase 1 Error: Could not process product identification. Details logged.")
            if 'finder_run' in locals() and finder_run and finder_run.content:
                st.code(finder_run.content, language='text')
            status1.update(label="Phase 1 Error: Processing failed.", state="error")
            phase1_results = None

    # Display Phase 1 results immediately
    if phase1_success and phase1_results:
        st.success(f"Phase 1 Complete: {len(phase1_results)} product(s) identified.")

        with st.expander("Phase 1 Results: Identified Products", expanded=True):
            display_data_p1 = []
            for p in phase1_results:
                if not isinstance(p, dict):
                    continue

                links_list = p.get("infringement_evidence_links", [])
                links_str = "N/A"
                if isinstance(links_list, list) and links_list:
                    markdown_links = [f"[{link.strip()}]({link.strip()})" for link in links_list if
                                      link and isinstance(link, str) and link.strip()]
                    if markdown_links:
                        links_str = "\n\n".join(markdown_links)
                display_data_p1.append({
                    "Company": p.get("company", "N/A"),
                    "Product/Model": p.get("model", "N/A"),
                    "Launch Date": format_date(p.get("launch_date", "N/A")),
                    "Evidence Links": links_str
                })

            if display_data_p1:
                st.dataframe(display_data_p1, use_container_width=True)
            else:
                st.info("No products were identified in Phase 1.")

        # Now continue with Phase 2 processing
        aggregated_phase2_results = []
        total_products_for_phase2 = len(phase1_results)

        with st.status(
                f"Phase 2: Performing enhanced claim chart analysis for {total_products_for_phase2} product(s)...",
                expanded=True) as status2:
            logging.info(f"Starting Phase 2 for {patent_number} with {total_products_for_phase2} products.")
            phase2_processed_count = 0
            phase2_successful_count = 0

            for product_to_analyze in phase1_results:
                # Ensure product_to_analyze is a dictionary
                if not isinstance(product_to_analyze, dict):
                    logging.warning(f"Phase 2: Skipping product because it's not a dictionary: {product_to_analyze}")
                    continue

                phase2_processed_count += 1
                company_name_log = product_to_analyze.get('company', 'N/A')
                model_name_log = product_to_analyze.get('model', 'N/A')
                current_product_label = f"{company_name_log} - {model_name_log}"

                status2.update(
                    label=f"Phase 2: Analyzing product {phase2_processed_count}/{total_products_for_phase2} ({current_product_label})...")
                logging.info(
                    f"Phase 2: Processing product {phase2_processed_count}/{total_products_for_phase2}: {current_product_label} for patent {patent_number}")

                # Process each product individually to avoid context overflow
                single_product_phase2_input_payload = {
                    "Patent Details": {
                        "Priority Date": formatted_priority_date,
                        "Independent Claims": independent_claims
                    },
                    "Identified Products List": [product_to_analyze]  # Just one product at a time
                }

                charting_query_single_product = (
                    f"Analyze the provided product against the patent details. "
                    f"Return ONLY the specified JSON list for claim chart analysis.\n\n"
                    f"**Patent Details:**\n"
                    f"  * Priority Date: {single_product_phase2_input_payload['Patent Details']['Priority Date']}\n"
                    f"  * Independent Claims ({len(single_product_phase2_input_payload['Patent Details']['Independent Claims'])} total):\n"
                    f"```json\n{json.dumps(single_product_phase2_input_payload['Patent Details']['Independent Claims'], indent=2)}\n```\n\n"
                    f"**Product to Analyze:**\n"
                    f"```json\n{json.dumps(single_product_phase2_input_payload['Identified Products List'][0], indent=2)}\n```\n\n"
                    f"**CRITICAL OUTPUT FORMAT REQUIREMENT:**\n"
                    f"You MUST return your response in this EXACT JSON format:\n"
                    f"```json\n"
                    f"[{{\n"
                    f"  \"company\": \"{company_name_log}\",\n"
                    f"  \"model\": \"{model_name_log}\",\n"
                    f"  \"category\": \"Product Category\",\n"
                    f"  \"activity_date\": \"YYYY-MM-DD or formatted date\",\n"
                    f"  \"infringement_risk\": \"High/Medium/Low/Error\",\n"
                    f"  \"risk_justification\": \"Detailed explanation of Infringement Risk\",\n"
                    f"  \"claim_chart\": [\n"
                    f"    {{\n"
                    f"      \"claim_element\": \"First claim element text\",\n"
                    f"      \"corresponding_feature\": \"Product feature description\",\n"
                    f"      \"source_justification\": \"Source citation and explanation\"\n"
                    f"    }},\n"
                    f"    {{\n"
                    f"      \"claim_element\": \"Second claim element text\",\n" 
                    f"      \"corresponding_feature\": \"Product feature description\",\n"
                    f"      \"source_justification\": \"Source citation and explanation\"\n"
                    f"    }}\n"
                    f"  ],\n"
                    f"  \"sources\": [\"source1\", \"source2\"],\n"
                    f"  \"search_queries\": [\"query1\", \"query2\"]\n"
                    f"}}]\n"
                    f"```\n\n"
                    f"IMPORTANT: Return ONLY valid JSON. No explanations before or after the JSON. Start with '[' and end with ']'."
                )

                error_message_for_product = None
                try:
                    charting_run_single = claim_chart_agent.run(charting_query_single_product)
                    if charting_run_single and charting_run_single.content:
                        raw_json_p2_single = charting_run_single.content
                        logging.debug(f"Phase 2 Raw Output for product {current_product_label}: {raw_json_p2_single}")

                        # Use enhanced JSON extraction
                        parsed_data_p2_single = extract_json_from_text(raw_json_p2_single)

                        if parsed_data_p2_single and isinstance(parsed_data_p2_single, list) and len(parsed_data_p2_single) > 0:
                            # Successfully parsed the JSON response
                            aggregated_phase2_results.extend(parsed_data_p2_single)
                            phase2_successful_count += 1
                            logging.info(
                                f"Phase 2 Success for product {phase2_processed_count}/{total_products_for_phase2}: {current_product_label}")
                        elif parsed_data_p2_single and isinstance(parsed_data_p2_single, dict):
                            # Handle case where response is a single dict instead of a list
                            aggregated_phase2_results.append(parsed_data_p2_single)
                            phase2_successful_count += 1
                            logging.info(
                                f"Phase 2 Success (dict format) for product {phase2_processed_count}/{total_products_for_phase2}: {current_product_label}")
                        else:
                            error_message_for_product = f"Failed to parse claim chart data from agent response. Type: {type(parsed_data_p2_single)}"
                            logging.warning(
                                f"Phase 2 Warning for {current_product_label}: {error_message_for_product}")
                            logging.debug(f"Raw response that failed to parse: {raw_json_p2_single[:500]}...")
                    else:
                        error_message_for_product = "Agent returned no content or run failed."
                        logging.warning(f"Phase 2 Warning for {current_product_label}: {error_message_for_product}")

                except Exception as e_single:
                    error_message_for_product = f"Unexpected error during agent run: {e_single}."
                    logging.error(
                        f"Error during Phase 2 agent run for {current_product_label}: {error_message_for_product}",
                        exc_info=True)
                    st.error(
                        f"An unexpected error occurred during claim chart analysis for {current_product_label}: {e_single}")

                if error_message_for_product:
                    # Create a properly formatted error entry
                    aggregated_phase2_results.append({
                        "company": company_name_log,
                        "model": model_name_log,
                        "category": "N/A",
                        "activity_date": "N/A",
                        "infringement_risk": "Error",
                        "risk_justification": f"Claim chart generation failed. {error_message_for_product}",
                        "claim_chart": [{"claim_element": "Error", "corresponding_feature": "Error",
                                         "source_justification": error_message_for_product}],
                        "sources": [],
                        "search_queries": []
                    })

            status2.update(label=f"Phase 2 Complete: Successfully analyzed {phase2_successful_count} out of {phase2_processed_count} products.")
            if phase2_successful_count > 0:
                status2.update(state="complete")
                logging.info(
                    f"Phase 2 Complete for {patent_number}: Successfully analyzed {phase2_successful_count}/{phase2_processed_count} products.")
                phase2_results = aggregated_phase2_results
            else:
                status2.update(state="error")
                logging.error(f"Phase 2 Failed for {patent_number}: No products were successfully analyzed.")
                phase2_results = []

    elif phase1_success and not phase1_results:
        st.info("Phase 1 identified 0 products. Skipping detailed claim chart analysis (Phase 2).")
        logging.info(f"Skipping Phase 2 for {patent_number} as Phase 1 found no products.")
        phase2_results = []
    elif phase1_results is None:
        st.warning("Phase 1 product identification failed. Skipping detailed claim chart analysis (Phase 2).")
        logging.warning(f"Skipping Phase 2 for {patent_number} due to Phase 1 failure.")
        phase2_results = None
    else:
        st.error("Phase 1 status is unclear. Skipping detailed claim chart analysis (Phase 2).")
        logging.error(f"Skipping Phase 2 for {patent_number} due to unclear Phase 1 status.")
        phase2_results = None

    # Save results to database
    if phase1_results is not None and (phase1_results or novelty_summary != "Error fetching patent data."):
        save_success = save_to_database(patent_number, formatted_priority_date, phase1_results, phase2_results, user_id,
                                        patent_title, assignees, competitors, novelty_summary)
        if save_success:
            st.success("✅ Analysis results successfully saved to database.")
            logging.info(f"Analysis results saved to database for patent {patent_number} and user {user_id}")

            # Verify database storage for this user
            conn = sqlite3.connect(WISSEN_DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM infringed_models WHERE patent_number = ? AND user_id = ?", (patent_number, user_id))
            model_count = cursor.fetchone()[0]
            cursor.execute(
                "SELECT COUNT(*) FROM claim_charts c JOIN infringed_models m ON c.infringed_model_id = m.id WHERE m.patent_number = ? AND m.user_id = ?",
                (patent_number, user_id))
            chart_count = cursor.fetchone()[0]
            conn.close()

            st.info(f"Database verification: {model_count} products and {chart_count} claim chart entries stored for your account.")
        else:
            st.warning("⚠️ Failed to save analysis results to database.")
            logging.warning(f"Failed to save analysis results to database for patent {patent_number}")

    return phase1_results, phase2_results, formatted_priority_date, competitors, novelty_summary, assignees


def init_session_state():
    defaults = {
        "authenticated": False,
        "user_data": None,
        "otp_required": False,
        "email": "",
        "password": "",
        "user_id": None
    }
    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value


def check_and_use_credit():
    from auth import check_credits_available, use_credit

    user_id = st.session_state.get("user_id")
    if not user_id:
        st.error("User ID not found. Please log in again.")
        return False

    credits_available, message = check_credits_available(user_id)
    if not credits_available:
        st.error(message)
        return False

    use_credit(user_id)
    return True


def display_user_patent_history():
    """Display user's patent analysis history in sidebar"""
    user_id = st.session_state.get("user_id")
    if not user_id:
        return

    try:
        conn = sqlite3.connect(WISSEN_DB_PATH)
        cursor = conn.cursor()

        # Get user's patent analysis history
        cursor.execute('''
            SELECT patent_number, title, inserted_at,
                   (SELECT COUNT(*) FROM infringed_models WHERE patent_number = pn.patent_number AND user_id = pn.user_id) as product_count
            FROM patent_novelty pn
            WHERE user_id = ?
            ORDER BY inserted_at DESC
            LIMIT 10
        ''', (user_id,))

        history = cursor.fetchall()
        conn.close()

        if history:
            st.markdown("---")
            st.subheader("Your Recent Analyses")

            for patent_number, title, inserted_at, product_count in history:
                # Format the date
                try:
                    date_obj = datetime.fromisoformat(inserted_at.replace('Z', '+00:00'))
                    formatted_date = date_obj.strftime("%m/%d/%Y")
                except:
                    formatted_date = inserted_at[:10] if inserted_at else "Unknown"

                # Create a clickable button for each patent
                display_title = title[:30] + "..." if title and len(title) > 30 else (title or "No Title")
                button_text = f"{patent_number}\n{display_title}\n{formatted_date} ({product_count} products)"

                if st.button(button_text, key=f"history_{patent_number}", help=f"Load analysis for {patent_number}"):
                    st.session_state.current_patent = patent_number
                    st.session_state.patent_input_value = patent_number
                    st.rerun()

    except Exception as e:
        logging.error(f"Error loading user patent history: {e}")
        # Don't show error to user as this is not critical functionality


def main():
    st.set_page_config(page_title="Patent Infringement Analyzer", page_icon="⚖️", layout="wide")
    st.title("⚖️ Patent Infringement Analysis")

    init_database()
    init_session_state()

    if not check_authentication():
        return

    if 'current_patent' not in st.session_state: st.session_state.current_patent = ""
    if 'analysis_running' not in st.session_state: st.session_state.analysis_running = False
    if 'phase1_results' not in st.session_state: st.session_state.phase1_results = None
    if 'phase2_results' not in st.session_state: st.session_state.phase2_results = None
    if 'patent_priority_date' not in st.session_state: st.session_state.patent_priority_date = "N/A"
    if 'patent_competitors' not in st.session_state: st.session_state.patent_competitors = []
    if 'patent_assignees' not in st.session_state: st.session_state.patent_assignees = []
    if 'patent_novelty_summary' not in st.session_state: st.session_state.patent_novelty_summary = "N/A"
    if 'patent_title_display' not in st.session_state: st.session_state.patent_title_display = ""

    default_patent = "US7504937B2"
    if 'patent_input_value' not in st.session_state:
        st.session_state.patent_input_value = st.session_state.current_patent or default_patent

    with st.sidebar:
        logo_base64, logo_loaded = img_to_base64("logo.png")
        if logo_loaded:
            st.markdown(f"""
                    <div style="text-align: center; margin-bottom: 20px;">
                        <img src="data:image/jpeg;base64,{logo_base64}" width="220">
                    </div>""", unsafe_allow_html=True)
        else:
            st.markdown("""
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h1 style="font-size: 24px;">Wissen Research</h1>
                    </div>""", unsafe_allow_html=True)
            logging.warning("Logo file 'logo.png' not found.")

        # Display user information
        user_data = st.session_state.get("user_data", {})
        user_id = st.session_state.get("user_id", "Unknown")
        user_email = user_data.get("email", f"User ID: {user_id}")
        session_token = st.session_state.get("session_token", "No Token")
        st.markdown(f"**Logged in as:** {user_email}")

        # Debug information (can be removed later)
        with st.expander("🔧 Debug Info", expanded=False):
            st.text(f"User ID: {user_id}")
            st.text(f"Session Token: {session_token[:8]}..." if session_token != "No Token" else "No Token")
            st.text(f"Authenticated: {st.session_state.get('authenticated', False)}")
            st.text(f"User Data Keys: {list(user_data.keys()) if user_data else 'None'}")

            # Force logout button for testing
            if st.button("🚪 Force Logout", help="Clear all authentication data"):
                from auth import clear_authentication
                clear_authentication()
                st.rerun()

        # Display user's patent analysis history
        display_user_patent_history()

        st.subheader("Analyze Patent")
        current_display_value = st.session_state.current_patent if st.session_state.current_patent else st.session_state.patent_input_value

        patent_number_input = st.text_input(
            "Enter Patent Number:",
            value=current_display_value,
            key="patent_input_widget",
            disabled=st.session_state.analysis_running,
            help="e.g., US7504937B2, US10000000A1, EP1234567A1"
        )

        if patent_number_input != st.session_state.patent_input_value:
            st.session_state.patent_input_value = patent_number_input

        analyze_button = st.button("Analyze Patent", key="analyze_button", disabled=st.session_state.analysis_running,
                                   type="primary", use_container_width=True)

        if st.session_state.authenticated and st.session_state.user_id:
            from auth import display_credits_sidebar
            display_credits_sidebar(st.session_state.user_id)

        if analyze_button:
            cleaned_patent_number = st.session_state.patent_input_value.strip().upper()

            if cleaned_patent_number and re.match(r"^[A-Z0-9]{2,}[\s\-A-Z0-9]{5,}$", cleaned_patent_number):
                user_id = st.session_state.get("user_id")
                if not user_id:
                    st.error("User authentication required. Please log in again.")
                    return

                # Check if patent exists in database for this user first
                if check_existing_analysis(cleaned_patent_number, user_id):
                    # Patent found in database for this user - don't use credit, just display results
                    st.info(
                        f"Found existing analysis for patent {cleaned_patent_number} in your account. Loading saved results...")
                    logging.info(f"Loading existing analysis for {cleaned_patent_number} from database for user {user_id}")

                    phase1_results, phase2_results, priority_date, competitors, novelty_summary, assignees = load_analysis_from_database(
                        cleaned_patent_number, user_id)

                    if phase1_results is not None:
                        st.session_state.current_patent = cleaned_patent_number
                        st.session_state.phase1_results = phase1_results
                        st.session_state.phase2_results = phase2_results
                        st.session_state.patent_priority_date = priority_date
                        st.session_state.patent_competitors = competitors
                        st.session_state.patent_novelty_summary = novelty_summary
                        st.session_state.patent_assignees = assignees
                        st.session_state.analysis_running = False

                        # Get patent title for display
                        pat_data_for_title = cached_get_patent_full_details(cleaned_patent_number)
                        st.session_state.patent_title_display = pat_data_for_title.get("title", "Title Not Found")

                        st.success(
                            f"Successfully loaded existing analysis with {len(phase1_results)} products from database.")
                        st.rerun()
                    else:
                        st.warning("Failed to load existing analysis from database. Proceeding with fresh analysis...")
                else:
                    # Patent not in database - check credit and run new analysis
                    if not check_and_use_credit():
                        st.session_state.analysis_running = False
                        return

                    if st.session_state.current_patent != cleaned_patent_number or not st.session_state.analysis_running:
                        st.session_state.analysis_running = True
                        st.session_state.current_patent = cleaned_patent_number
                        st.session_state.phase1_results = None
                        st.session_state.phase2_results = None
                        st.session_state.patent_priority_date = "N/A"
                        st.session_state.patent_competitors = []
                        st.session_state.patent_assignees = []
                        st.session_state.patent_novelty_summary = "N/A"
                        st.session_state.patent_title_display = ""

                        pat_data_for_title = cached_get_patent_full_details(cleaned_patent_number)
                        st.session_state.patent_title_display = pat_data_for_title.get("title", "Title Not Found")

                        logging.info(f"Starting enhanced analysis for patent: {st.session_state.current_patent}")
                        st.rerun()
            else:
                st.error("Invalid patent number format. Examples: US9253445B2, EP1234567A1, WO2020000001A1.")
                logging.warning(f"Invalid patent number format entered: {st.session_state.patent_input_value}")
                st.session_state.analysis_running = False

    # Add this function to ensure all items in results are dictionaries
    def ensure_dict_items(items_list):
        if not isinstance(items_list, list):
            return []

        result = []
        for item in items_list:
            if isinstance(item, dict):
                result.append(item)
            elif isinstance(item, str):
                try:
                    dict_item = json.loads(item)
                    if isinstance(dict_item, dict):
                        result.append(dict_item)
                    else:
                        logging.warning(f"Parsed item is not a dictionary: {type(dict_item)}")
                except json.JSONDecodeError:
                    logging.warning(f"Failed to parse string as JSON: {item[:100]}...")
            else:
                logging.warning(f"Skipping non-dictionary, non-string item: {type(item)}")
        return result

    if st.session_state.analysis_running and st.session_state.current_patent:
        # Display user context
        user_data = st.session_state.get("user_data", {})
        user_id = st.session_state.get("user_id", "Unknown")
        user_email = user_data.get("email", f"User ID: {user_id}")
        st.info(f"🔍 Running analysis for user: **{user_email}**")

        header_text = f"Analyzing Patent: {st.session_state.current_patent}"
        if st.session_state.patent_title_display and st.session_state.patent_title_display != "Title Not Found":
            header_text += f" - {st.session_state.patent_title_display}"
        st.header(header_text)

        p1_results, p2_results, priority_date, competitors_list, novelty, assignees_list = analyze_patent(
            st.session_state.current_patent)

        # Convert all items to dictionaries
        p1_results = ensure_dict_items(p1_results)
        p2_results = ensure_dict_items(p2_results)

        st.session_state.phase1_results = p1_results
        st.session_state.phase2_results = p2_results
        st.session_state.patent_priority_date = priority_date
        st.session_state.patent_competitors = competitors_list
        st.session_state.patent_novelty_summary = novelty
        st.session_state.patent_assignees = assignees_list
        st.session_state.analysis_running = False
        logging.info(f"Enhanced analysis completed for {st.session_state.current_patent}.")
        st.rerun()

    elif st.session_state.current_patent and not st.session_state.analysis_running:
        # Display user context for results
        user_data = st.session_state.get("user_data", {})
        user_id = st.session_state.get("user_id", "Unknown")
        user_email = user_data.get("email", f"User ID: {user_id}")
        st.info(f"📊 Analysis results for user: **{user_email}**")

        header_text = f"Analysis Results for Patent: {st.session_state.current_patent}"
        if st.session_state.patent_title_display and st.session_state.patent_title_display != "Title Not Found":
            header_text += f" - {st.session_state.patent_title_display}"
        st.header(header_text)

        # Display patent metadata
        with st.expander("Patent Information", expanded=False):
            st.markdown(f"**Priority Date:** {st.session_state.patent_priority_date}")
            st.markdown(f"**Novelty Summary:**\n{st.session_state.patent_novelty_summary}")

            if st.session_state.patent_assignees:
                st.markdown(f"**Assignees:** {', '.join(st.session_state.patent_assignees)}")

            if st.session_state.patent_competitors:
                st.markdown(f"**Potential Competitors:** {', '.join(st.session_state.patent_competitors)}")

        # Phase 1 Results
        p1_results_display = ensure_dict_items(st.session_state.phase1_results)
        if isinstance(p1_results_display, list) and p1_results_display:
            with st.expander("Phase 1: Identified Products", expanded=True):
                st.success(f"Found {len(p1_results_display)} potentially infringing products")
                display_data_p1 = []
                for p in p1_results_display:
                    links_list = p.get("infringement_evidence_links", [])
                    links_str = "N/A"
                    if isinstance(links_list, list) and links_list:
                        markdown_links = [f"[{link.strip()}]({link.strip()})" for link in links_list if
                                          link and isinstance(link, str) and link.strip()]
                        if markdown_links:
                            links_str = "\n\n".join(markdown_links)
                    display_data_p1.append({
                        "Company": p.get("company", "N/A"),
                        "Product/Model": p.get("model", "N/A"),
                        "Launch Date": format_date(p.get("launch_date", "N/A")),
                        "Evidence Links": links_str
                    })

                if display_data_p1:
                    st.dataframe(display_data_p1, use_container_width=True)
        else:
            st.info("No potentially infringing products were identified.")

        # Display independent claims
        patent_data = cached_get_patent_full_details(st.session_state.current_patent)
        independent_claims = patent_data.get("independent_claims", [])
        st.subheader("Independent Claims")
        with st.expander("View Independent Claims", expanded=False):
            for i, claim in enumerate(independent_claims):
                st.markdown(f"{claim}")

        # Phase 2 Results
        p2_results_display = ensure_dict_items(st.session_state.phase2_results)
        phase2_status = "complete" if isinstance(p2_results_display, list) else "processing"

        with st.expander("Phase 2: Detailed Claim Charts", expanded=True):
            if phase2_status == "processing":
                st.info("Claim charts are still being generated. This may take several minutes...")
                st.spinner("Processing claim charts...")
            elif isinstance(p2_results_display, list) and p2_results_display:
                st.success(f"Generated claim charts for {len(p2_results_display)} products")

                # Create tabs for each product's claim chart
                product_tabs = []
                valid_charts = []

                # First, filter and prepare valid charts
                for chart in p2_results_display:
                    if not isinstance(chart, dict):
                        continue

                    company = chart.get("company", "Unknown Company")
                    model = chart.get("model", "Unknown Model")

                    if company == "Unknown Company" and model == "Unknown Model":
                        continue

                    valid_charts.append(chart)
                    product_tabs.append(f"{company} - {model}")

                if valid_charts:
                    # Display each claim chart sequentially instead of in tabs
                    for i, chart in enumerate(valid_charts):
                        # Add a separator between charts (except for the first one)
                        if i > 0:
                            st.divider()

                        # Display claim chart details
                        company = chart.get("company", "Unknown Company")
                        model = chart.get("model", "Unknown Model")
                        risk_level = chart.get("infringement_risk", chart.get("infringement_risk_level", "Unknown"))
                        risk_explanation = chart.get("risk_justification",
                                                     chart.get("infringement_risk_explanation",
                                                               "No explanation provided"))

                        # Create a header for each product
                        st.subheader(f"{company} - {model}")

                        # Color-code the risk level
                        risk_color = "gray"
                        if risk_level and isinstance(risk_level, str):
                            risk_level_lower = risk_level.lower()
                            if "high" in risk_level_lower:
                                risk_color = "red"
                            elif "medium" in risk_level_lower:
                                risk_color = "orange"
                            elif "low" in risk_level_lower:
                                risk_color = "green"

                        st.markdown(
                            f"**Infringement Risk:** <span style='color:{risk_color};font-weight:bold;'>{risk_level}</span>",
                            unsafe_allow_html=True)
                        st.markdown(f"**Justification:** {risk_explanation}")

                        claim_chart_data = chart.get("claim_chart", [])
                        if claim_chart_data:
                            # Display claim chart data
                            chart_rows = []
                            for entry in claim_chart_data:
                                if not isinstance(entry, dict):
                                    continue

                                claim_element = entry.get("claim_element", entry.get("claim_limitation", "N/A"))
                                product_feature = entry.get("product_element",
                                                            entry.get("corresponding_feature", "N/A"))
                                explanation = entry.get("match_explanation", entry.get("source_justification", "N/A"))

                                chart_rows.append({
                                    "Claim Element": claim_element,
                                    "Product Feature": product_feature,
                                    "Explanation": explanation
                                })

                            if chart_rows:
                                st.dataframe(chart_rows, use_container_width=True)
                            else:
                                st.info("No claim chart details available.")
                        else:
                            st.info("No claim chart details available for this product.")
                else:
                    st.info("No valid claim charts with known company and model information were found.")
            else:
                st.info("No claim charts were generated. This could be because no infringement was found.")

                # Download button - only show when both phases are complete
            if isinstance(p1_results_display, list) and p1_results_display:
                if phase2_status == "complete":
                    excel_buffer = create_excel_analysis(p1_results_display, p2_results_display or [])
                    st.download_button(
                        label="Download Complete Analysis as Excel",
                        data=excel_buffer,
                        file_name=f"Patent_Analysis_{st.session_state.current_patent}_{datetime.now().strftime('%Y%m%d_%H%M')}.xlsx",
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        use_container_width=True
                    )
                    with st.form(key='relevance_feedback_form'):
                        # Display products with feedback options
                        for idx, product in enumerate(p1_results_display):
                            st.subheader(f"Product: {product.get('model', 'N/A')}")
                            st.markdown(f"**Company:** {product.get('company', 'N/A')}")

                            # Create columns for layout
                            col1, col2 = st.columns([1, 3])

                            with col1:
                                # Relevance selection
                                relevance_options = ["Not Rated", "Relevant", "Not Relevant"]
                                current_relevance = product.get('is_relevant', None)
                                current_index = 0  # Default to "Not Rated"

                                if current_relevance == 1:
                                    current_index = 1
                                elif current_relevance == 0:
                                    current_index = 2

                                selected_relevance = st.radio(
                                    "Is this product relevant?",
                                    relevance_options,
                                    index=current_index,
                                    key=f"relevance_{idx}"
                                )

                                # Map selection to database value
                                is_relevant = None
                                if selected_relevance == "Relevant":
                                    is_relevant = 1
                                elif selected_relevance == "Not Relevant":
                                    is_relevant = 0

                            with col2:
                                # Reason input
                                current_reason = product.get('relevance_reason', '')
                                reason = st.text_area(
                                    "Reason for relevance assessment:",
                                    value=current_reason,
                                    height=100,
                                    key=f"reason_{idx}"
                                )

                            # Store updated values in session state
                            st.session_state.phase1_results[idx]['is_relevant'] = is_relevant
                            st.session_state.phase1_results[idx]['relevance_reason'] = reason

                            st.divider()

                        # Submit button for all feedback
                        submitted = st.form_submit_button("Save All Feedback")
                        if submitted:
                            user_id = st.session_state.get("user_id")
                            if user_id:
                                save_feedback_to_database(st.session_state.current_patent, p1_results_display, user_id)
                                st.success("Feedback saved successfully!")
                            else:
                                st.error("User authentication required. Please log in again.")
                else:
                    # Offer Phase 1 only download while Phase 2 is still processing
                    excel_buffer = create_excel_analysis(p1_results_display, [])
                    st.download_button(
                        label="Download Phase 1 Results as Excel (Phase 2 still processing)",
                        data=excel_buffer,
                        file_name=f"Patent_Analysis_{st.session_state.current_patent}_Phase1_{datetime.now().strftime('%Y%m%d_%H%M')}.xlsx",
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        use_container_width=True
                    )

            else:
                st.info("Enter a patent number in the sidebar and click 'Analyze Patent' to begin enhanced analysis.")
if __name__ == "__main__":
    main()